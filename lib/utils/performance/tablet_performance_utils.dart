import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/frame_rate_monitor.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// Tablet-specific performance metrics collection utilities
///
/// Provides specialized tracking for:
/// - NavigationRail render time monitoring
/// - Master-detail layout performance analysis
/// - WebView frame drop detection
/// - Complex widget tree analysis
class TabletPerformanceUtils {
  static TabletPerformanceUtils? _instance;
  static TabletPerformanceUtils get instance => _instance ??= TabletPerformanceUtils._();
  TabletPerformanceUtils._();

  TabletPerformanceMonitor? _monitor;
  final Map<String, Stopwatch> _componentStopwatches = {};
  final Map<String, List<Duration>> _renderTimeHistory = {};

  /// Initialize tablet performance utilities
  void initialize(TabletPerformanceMonitor monitor) {
    _monitor = monitor;
    _initializeComponentTracking();
    
    if (kDebugMode) {
      AnxLog.info('📊 TabletPerformanceUtils: Initialized with component tracking');
    }
  }

  /// Initialize component tracking
  void _initializeComponentTracking() {
    final components = [
      'navigationRail',
      'masterDetailLayout',
      'sideMenu',
      'searchBar',
      'profilePane',
      'webView',
      'bookList',
      'settingsPanel',
    ];

    for (final component in components) {
      _componentStopwatches[component] = Stopwatch();
      _renderTimeHistory[component] = <Duration>[];
    }
  }

  /// Start tracking component render time
  void startComponentTracking(String component) {
    final stopwatch = _componentStopwatches[component];
    if (stopwatch != null) {
      stopwatch.reset();
      stopwatch.start();
    }
  }

  /// Stop tracking component render time and record metrics
  void stopComponentTracking(String component) {
    final stopwatch = _componentStopwatches[component];
    if (stopwatch != null && stopwatch.isRunning) {
      stopwatch.stop();
      final renderTime = stopwatch.elapsed;
      
      // Record in history
      final history = _renderTimeHistory[component];
      if (history != null) {
        history.add(renderTime);
        
        // Maintain history size
        while (history.length > 20) {
          history.removeAt(0);
        }
      }
      
      // Track with monitor
      _monitor?.trackComponentRenderTime(component, renderTime);
      
      // Log significant render times
      if (kDebugMode && renderTime.inMilliseconds > 20) {
        AnxLog.info(
          '📊 Component: $component rendered in ${renderTime.inMilliseconds}ms'
        );
      }
    }
  }

  /// Track NavigationRail specific metrics
  void trackNavigationRailMetrics({
    required int menuItemCount,
    required bool hasSearchBar,
    required bool isExpanded,
    required int activeAnimations,
  }) {
    if (_monitor == null) return;

    // Calculate NavigationRail complexity
    int complexity = menuItemCount * 2; // Base complexity
    if (hasSearchBar) complexity += 5;
    if (isExpanded) complexity += 3;
    complexity += activeAnimations * 2;

    _monitor!.trackLayoutComplexity(
      visibleComponents: menuItemCount + (hasSearchBar ? 1 : 0),
      simultaneousAnimations: activeAnimations,
    );

    if (kDebugMode) {
      AnxLog.info(
        '🧭 NavigationRail: $menuItemCount items, '
        'searchBar: $hasSearchBar, expanded: $isExpanded, '
        'animations: $activeAnimations, complexity: $complexity'
      );
    }
  }

  /// Track master-detail layout performance
  void trackMasterDetailMetrics({
    required bool isMasterVisible,
    required bool isDetailVisible,
    required int activeProviders,
    required bool isTransitioning,
  }) {
    if (_monitor == null) return;

    int visibleComponents = 0;
    if (isMasterVisible) visibleComponents++;
    if (isDetailVisible) visibleComponents++;

    _monitor!.trackLayoutComplexity(
      activeProviders: activeProviders,
      visibleComponents: visibleComponents,
      simultaneousAnimations: isTransitioning ? 1 : 0,
    );

    if (kDebugMode) {
      AnxLog.info(
        '📱 MasterDetail: master: $isMasterVisible, detail: $isDetailVisible, '
        'providers: $activeProviders, transitioning: $isTransitioning'
      );
    }
  }

  /// Track WebView performance metrics
  void trackWebViewMetrics({
    required bool isLoading,
    required int frameDrops,
    required Duration lastRenderTime,
    required String contentType,
  }) {
    if (_monitor == null) return;

    // Track WebView render time
    _monitor!.trackComponentRenderTime('webView', lastRenderTime);

    // Log WebView performance issues
    if (frameDrops > 0 && kDebugMode) {
      AnxLog.warning(
        '📖 WebView: $frameDrops frame drops detected for $contentType content'
      );
    }

    if (lastRenderTime.inMilliseconds > 30 && kDebugMode) {
      AnxLog.warning(
        '📖 WebView: Slow render time ${lastRenderTime.inMilliseconds}ms '
        'for $contentType'
      );
    }
  }

  /// Analyze widget tree complexity
  void analyzeWidgetTreeComplexity(BuildContext context) {
    if (_monitor == null) return;

    // Calculate widget tree depth (simplified estimation)
    int depth = 0;
    BuildContext? currentContext = context;
    
    // Traverse up the widget tree to estimate depth
    while (currentContext != null && depth < 50) { // Safety limit
      depth++;
      try {
        currentContext = currentContext.findAncestorWidgetOfExactType<Widget>()?.createElement();
      } catch (e) {
        break; // Stop if we can't traverse further
      }
    }

    _monitor!.trackLayoutComplexity(widgetTreeDepth: depth);

    if (kDebugMode && depth > 20) {
      AnxLog.info('🌳 Widget tree depth: $depth (consider optimization if >25)');
    }
  }

  /// Get component performance summary
  Map<String, dynamic> getComponentPerformanceSummary() {
    final summary = <String, dynamic>{};
    
    _renderTimeHistory.forEach((component, history) {
      if (history.isNotEmpty) {
        final avgMs = history
            .map((d) => d.inMicroseconds / 1000)
            .reduce((a, b) => a + b) / history.length;
        
        final maxMs = history
            .map((d) => d.inMicroseconds / 1000)
            .reduce((a, b) => a > b ? a : b);
        
        summary[component] = {
          'averageMs': avgMs.toStringAsFixed(1),
          'maxMs': maxMs.toStringAsFixed(1),
          'sampleCount': history.length,
        };
      }
    });
    
    return summary;
  }

  /// Check if device is in tablet mode
  bool isTabletMode(BuildContext context) {
    return DesignSystem.isTablet(context);
  }

  /// Check if device is in landscape mode
  bool isLandscapeMode(BuildContext context) {
    return ResponsiveSystem.getOrientation(context) == Orientation.landscape;
  }

  /// Update form factor for monitoring
  void updateFormFactor(BuildContext context) {
    if (_monitor == null) return;
    
    final isTablet = isTabletMode(context);
    final isLandscape = isLandscapeMode(context);
    
    _monitor!.updateFormFactor(isTablet, isLandscape);
  }

  /// Dispose resources
  void dispose() {
    _componentStopwatches.clear();
    _renderTimeHistory.clear();
    _monitor = null;
    
    if (kDebugMode) {
      AnxLog.info('📊 TabletPerformanceUtils: Disposed');
    }
  }
}

/// Widget wrapper for automatic performance tracking
class PerformanceTrackingWidget extends StatefulWidget {
  const PerformanceTrackingWidget({
    super.key,
    required this.componentName,
    required this.child,
    this.trackComplexity = false,
  });

  final String componentName;
  final Widget child;
  final bool trackComplexity;

  @override
  State<PerformanceTrackingWidget> createState() => _PerformanceTrackingWidgetState();
}

class _PerformanceTrackingWidgetState extends State<PerformanceTrackingWidget> {
  @override
  void initState() {
    super.initState();
    
    // Start tracking when widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabletPerformanceUtils.instance.startComponentTracking(widget.componentName);
    });
  }

  @override
  void didUpdateWidget(PerformanceTrackingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Track rebuilds
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TabletPerformanceUtils.instance.startComponentTracking(widget.componentName);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Update form factor
    TabletPerformanceUtils.instance.updateFormFactor(context);
    
    // Analyze widget tree complexity if requested
    if (widget.trackComplexity) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        TabletPerformanceUtils.instance.analyzeWidgetTreeComplexity(context);
      });
    }
    
    return widget.child;
  }

  @override
  void dispose() {
    // Stop tracking when widget is disposed
    TabletPerformanceUtils.instance.stopComponentTracking(widget.componentName);
    super.dispose();
  }
}
